import { pricingPlans } from "@/config/pricing";
import { initializePaddle as paddleInit } from "@paddle/paddle-js";

// Initialize Paddle client - this runs on the client side
export async function initializePaddle() {
  if (typeof window === "undefined") return null;

  const environment = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as "sandbox" | "production";
  const token = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN;

  if (!token) {
    console.error("Paddle client token is not configured");
    return null;
  }

  try {
    const paddle = await paddleInit({
      token,
      environment: environment || "sandbox",
      checkout: {
        settings: {
          displayMode: "overlay",
          theme: "light",
        },
      },
    });
    return paddle;
  } catch (error) {
    console.error("Failed to initialize Paddle:", error);
    return null;
  }
}

// Product configuration
export const PADDLE_PRODUCTS = {
  pro_monthly: {
    priceId: process.env.NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRICE_ID || "",
    productId: process.env.NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRODUCT_ID || "",
    name: pricingPlans.find(plan => plan.id === "pro_monthly")?.name || "Pro Monthly",
    price: pricingPlans.find(plan => plan.id === "pro_monthly")?.price || "$9.99/month",
  },
  pro_yearly: {
    priceId: process.env.NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRICE_ID || "",
    productId: process.env.NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRODUCT_ID || "",
    name: pricingPlans.find(plan => plan.id === "pro_yearly")?.name || "Pro Yearly",
    price: pricingPlans.find(plan => plan.id === "pro_yearly")?.price || "$99.99/year",
  },
};

// Paddle webhook event types we care about
export enum PaddleEventType {
  TransactionCompleted = "transaction.completed",
  TransactionUpdated = "transaction.updated",
  SubscriptionCreated = "subscription.created",
  SubscriptionUpdated = "subscription.updated",
  SubscriptionCanceled = "subscription.canceled",
  SubscriptionPaused = "subscription.paused",
  SubscriptionResumed = "subscription.resumed",
}

// Type for the webhook payload
export interface PaddleWebhookPayload {
  event_id: string;
  event_type: string;
  occurred_at: string;
  data: {
    id: string;
    status: string;
    customer_id: string | null;
    custom_data?: {
      userId?: string;
    };
    items: Array<{
      price_id: string;
      product_id?: string;
      quantity: number;
    }>;
    details?: {
      tax_amount: string;
      totals: {
        total: string;
        tax: string;
        subtotal: string;
      };
    };
    // Subscription specific fields
    subscription_id?: string;
    scheduled_change?: {
      action: string;
      effective_at: string;
    };
    billing_cycle?: {
      interval: string;
      frequency: number;
    };
    current_billing_period?: {
      starts_at: string;
      ends_at: string;
    };
  };
}

// Helper function to determine plan type from price ID
export function getPlanTypeFromPriceId(priceId: string): "pro_monthly" | "pro_yearly" | null {
  if (priceId === PADDLE_PRODUCTS.pro_monthly.priceId) {
    return "pro_monthly";
  }
  if (priceId === PADDLE_PRODUCTS.pro_yearly.priceId) {
    return "pro_yearly";
  }
  return null;
}
