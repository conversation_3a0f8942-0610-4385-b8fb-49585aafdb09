# Paddle Integration Setup Guide

## Overview
QuickCV uses Paddle for subscription management with two Pro plans:
- **Pro Monthly**: $9.99/month
- **Pro Yearly**: $99.99/year (20% discount)

## Setup Steps

### 1. Create Products in Paddle Dashboard

#### Pro Monthly Subscription
- **Name**: QuickCV Pro Monthly
- **Type**: Subscription
- **Billing**: Monthly
- **Price**: $9.99 USD

#### Pro Yearly Subscription  
- **Name**: QuickCV Pro Yearly
- **Type**: Subscription
- **Billing**: Annual
- **Price**: $99.99 USD

### 2. Configure Environment Variables

Copy `.env.paddle.example` to `.env.local` and fill in:

```env
# Paddle Environment
NEXT_PUBLIC_PADDLE_ENVIRONMENT=sandbox  # or "production"
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=test_xxxx

# Product IDs from Paddle
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRICE_ID=pri_xxxx
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRODUCT_ID=pro_xxxx
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRICE_ID=pri_yyyy
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRODUCT_ID=pro_yyyy

# Server-side
PADDLE_API_KEY=pdl_xxxx
PADDLE_WEBHOOK_SECRET=pdl_whsec_xxxx
```

### 3. Configure Webhooks in Paddle

**Webhook URL**: `https://yourdomain.com/api/webhook/paddle`

**Events to Enable**:
- `transaction.completed`
- `transaction.updated`
- `subscription.created`
- `subscription.updated`
- `subscription.canceled`

### 4. Testing in Sandbox

1. Use Paddle's test card numbers
2. Verify webhook events are received
3. Check user subscription status updates
4. Test both monthly and yearly flows

## Implementation Details

### Subscription Flow

1. **User clicks upgrade** → `useCheckout` hook
2. **Prepare checkout** → `/api/checkout` endpoint
3. **Open Paddle overlay** → Customer completes payment
4. **Webhook received** → `/api/webhook/paddle` processes event
5. **Update user** → Database updated with subscription details
6. **Access granted** → User has Pro features

### Database Fields

The `users` table tracks:
- `planId`: "free", "pro_monthly", or "pro_yearly"
- `subscriptionId`: Paddle subscription ID
- `currentPeriodEnd`: Subscription end date
- `paddleCustomerId`: Paddle customer ID
- `paymentId`: Transaction ID
- `paymentGateway`: "paddle"

### Webhook Event Handling

- **transaction.completed**: Initial purchase
- **subscription.created**: New subscription started
- **subscription.updated**: Plan change or renewal
- **subscription.canceled**: Cancellation (downgrade to free)

## Troubleshooting

### Common Issues

1. **Webhook signature fails**
   - Verify `PADDLE_WEBHOOK_SECRET` is correct
   - Check webhook URL in Paddle dashboard

2. **Price ID not found**
   - Ensure environment variables are set
   - Verify product IDs match Paddle dashboard

3. **User not updated after payment**
   - Check webhook logs in Paddle dashboard
   - Verify custom data includes userId

### Testing Checklist

- [ ] Create products in Paddle
- [ ] Configure environment variables
- [ ] Set up webhook endpoint
- [ ] Test monthly subscription
- [ ] Test yearly subscription
- [ ] Verify webhook processing
- [ ] Test subscription cancellation
- [ ] Verify user access control

## Support

For issues with Paddle integration:
1. Check webhook logs in Paddle dashboard
2. Review server logs for webhook errors
3. Verify environment variables are set correctly
4. Contact Paddle support for account issues