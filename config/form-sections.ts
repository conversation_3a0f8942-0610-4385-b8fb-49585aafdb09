import { FullResume } from "@/db/schema";

export interface FormSectionConfig {
  id: string;
  titleKey: string;
  description?: string;
  icon: string;
  collectionName: keyof FullResume;
  keyField: string;
  entity: string;
  fields: FormFieldConfig[];
}

export interface FormFieldConfig {
  name: string;
  type: "string" | "number" | "date" | "boolean" | "textarea";
  labelKey?: string;
  placeholder?: string;
  description?: string;
  className?: string;
  required?: boolean;
}

export const FORM_SECTIONS: FormSectionConfig[] = [
  {
    id: "profiles",
    titleKey: "Profile",
    description: "Social media and professional profiles",
    icon: "lucide:link",
    collectionName: "profiles",
    keyField: "network",
    entity: "profile",
    fields: [
      { name: "network", type: "string", placeholder: "LinkedIn, GitHub, Twitter" },
      { name: "username", type: "string", placeholder: "johndoe" },
      { name: "url", type: "string", placeholder: "https://linkedin.com/in/johndoe" },
    ],
  },
  {
    id: "education",
    titleKey: "Education",
    description: "Educational background and qualifications",
    icon: "lucide:graduation-cap",
    collectionName: "educations",
    keyField: "institution",
    entity: "education",
    fields: [
      { name: "institution", type: "string", placeholder: "University of Example" },
      { name: "degree", type: "string", placeholder: "Bachelor of Science" },
      { name: "field", type: "string", placeholder: "Computer Science" },
      { name: "startDate", type: "date", placeholder: "From" },
      { name: "endDate", type: "date", placeholder: "To" },
      { name: "gpa", type: "string", placeholder: "3.8/4.0" },
      { name: "city", type: "string", placeholder: "New York" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "experience",
    titleKey: "Experience",
    description: "Professional work experience",
    icon: "lucide:briefcase",
    collectionName: "experiences",
    keyField: "company",
    entity: "experience",
    fields: [
      { name: "company", type: "string", placeholder: "Example Corp" },
      { name: "position", type: "string", placeholder: "Software Engineer" },
      { name: "startDate", type: "date", placeholder: "From" },
      { name: "endDate", type: "date", placeholder: "To" },
      { name: "city", type: "string", placeholder: "San Francisco" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "projects",
    titleKey: "Projects",
    description: "Personal and professional projects",
    icon: "lucide:folder",
    collectionName: "projects",
    keyField: "title",
    entity: "project",
    fields: [
      { name: "title", type: "string", placeholder: "E-commerce Platform" },
      { name: "client", type: "string", placeholder: "Client Name" },
      { name: "startDate", type: "date", placeholder: "From" },
      { name: "endDate", type: "date", placeholder: "To" },
      { name: "url", type: "string", placeholder: "https://example.com" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "awards",
    titleKey: "Awards",
    description: "Achievements and recognitions",
    icon: "lucide:award",
    collectionName: "awards",
    keyField: "title",
    entity: "award",
    fields: [
      { name: "title", type: "string", placeholder: "Employee of the Year" },
      { name: "issuer", type: "string", placeholder: "Example Corp" },
      { name: "date", type: "date", placeholder: "Date" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "certifications",
    titleKey: "Certifications",
    description: "Professional certifications",
    icon: "lucide:certificate",
    collectionName: "certifications",
    keyField: "title",
    entity: "certification",
    fields: [
      { name: "title", type: "string", placeholder: "AWS Certified Developer" },
      { name: "issuer", type: "string", placeholder: "Amazon Web Services" },
      { name: "date", type: "date", placeholder: "Date" },
      { name: "url", type: "string", placeholder: "https://credly.com/badges/example" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "skills",
    titleKey: "Skills",
    description: "Technical and soft skills",
    icon: "lucide:zap",
    collectionName: "skills",
    keyField: "name",
    entity: "skill",
    fields: [
      { name: "name", type: "string", placeholder: "JavaScript" },
      { name: "level", type: "number", placeholder: "8 (out of 10)" },
    ],
  },
  {
    id: "languages",
    titleKey: "Languages",
    description: "Language proficiencies",
    icon: "lucide:globe",
    collectionName: "languages",
    keyField: "name",
    entity: "language",
    fields: [
      { name: "name", type: "string", placeholder: "English" },
      { name: "proficiency", type: "number", placeholder: "9 (out of 10)" },
    ],
  },
  {
    id: "references",
    titleKey: "References",
    description: "Professional references",
    icon: "lucide:users",
    collectionName: "references",
    keyField: "name",
    entity: "reference",
    fields: [
      { name: "name", type: "string", placeholder: "John Smith" },
      { name: "company", type: "string", placeholder: "Example Corp" },
      { name: "position", type: "string", placeholder: "Senior Manager" },
      { name: "email", type: "string", placeholder: "<EMAIL>" },
      { name: "phone", type: "string", placeholder: "+****************" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "hobbies",
    titleKey: "Hobbies",
    description: "Personal interests and hobbies",
    icon: "lucide:heart",
    collectionName: "hobbies",
    keyField: "name",
    entity: "hobby",
    fields: [
      { name: "name", type: "string", placeholder: "Photography" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
  {
    id: "volunteerings",
    titleKey: "Volunteering",
    description: "Volunteer work and community service",
    icon: "lucide:hand-heart",
    collectionName: "volunteerings",
    keyField: "role",
    entity: "volunteering",
    fields: [
      { name: "organization", type: "string", placeholder: "Red Cross" },
      { name: "role", type: "string", placeholder: "Volunteer Coordinator" },
      { name: "startDate", type: "date", placeholder: "From" },
      { name: "endDate", type: "date", placeholder: "To" },
      { name: "city", type: "string", placeholder: "Boston" },
      { name: "description", type: "textarea", labelKey: "Description", className: "col-span-full" },
    ],
  },
];

// Helper function to get section by ID
export function getFormSection(id: string): FormSectionConfig | undefined {
  return FORM_SECTIONS.find((section) => section.id === id);
}

// Helper function to create empty item for a section
export function createEmptyItem(section: FormSectionConfig, resumeId: number): Record<string, any> {
  const emptyItem: Record<string, any> = {
    resumeId,
    sort: 0, // Will be updated when added to collection
  };

  section.fields.forEach((field) => {
    switch (field.type) {
      case "boolean":
        emptyItem[field.name] = false;
        break;
      case "number":
        emptyItem[field.name] = 0;
        break;
      default:
        emptyItem[field.name] = "";
    }
  });

  return emptyItem;
}
