export type PlanId = "free" | "pro_monthly" | "pro_yearly";

export interface PricingPlan {
  id: PlanId;
  name: string;
  description: string;
  price: string;
  price_id?: string; // For payment providers like Stripe/Paddle
  features: string[];
  is_most_popular?: boolean;
}

export const RESUME_FREE_LIMIT = 1;

export const pricingPlans: PricingPlan[] = [
  {
    id: "free",
    name: "Free",
    description: "For getting started",
    price: "Free",
    features: ["Create 1 resume", "Access to basic templates", "Full resume editor access"],
  },
  {
    id: "pro_monthly",
    name: "Pro Monthly",
    description: "For professionals",
    price: "$9.99/month",
    price_id: "price_pro_monthly", // Example ID
    features: [
      "Create unlimited resumes",
      "Unlimited PDF exports",
      "Publish resumes to the web",
      "Access all premium templates",
      "AI-powered content suggestions",
      "Priority support",
    ],
    is_most_popular: true,
  },
  {
    id: "pro_yearly",
    name: "Pro Yearly",
    description: "For savvy professionals",
    price: "$99.99/year",
    price_id: "price_pro_yearly", // Example ID
    features: ["All features of Pro Monthly", "Save 20% with annual billing"],
  },
];
