import { clerkClient } from "@clerk/nextjs/server";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  hobbies,
  languages,
  profiles,
  projects,
  references,
  resumes,
  skills,
  users,
  volunteerings,
  websites,
} from "@/db/schema";
import { paddleServer } from "@/lib/paddle-server";
import { protectedProcedure, router } from "../trpc";

export const userRouter = router({
  // Get current user with premium status (basic info)
  getCurrentUser: protectedProcedure.query(async ({ ctx }) => {
    return {
      id: ctx.user.id,
      clerkId: ctx.user.clerkId,
      emailAddress: ctx.user.emailAddress,
      isPremium: ctx.user.planId !== "free",
      planId: ctx.user.planId,
    };
  }),

  // Get user profile information (all profile fields)
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const user = await db
      .select({
        id: users.clerkId,
        firstName: users.firstName,
        lastName: users.lastName,
        jobTitle: users.jobTitle,
        phone: users.phone,
        email: users.emailAddress,
        website: users.website,
        bio: users.bio,
        address: users.address,
        street: users.street,
        city: users.city,
        country: users.country,
      })
      .from(users)
      .where(eq(users.clerkId, ctx.user.clerkId))
      .limit(1);

    if (!user[0]) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User profile not found",
      });
    }

    return user[0];
  }),

  // Get comprehensive user information (combines basic + profile + plan info)
  getFullUser: protectedProcedure.query(async ({ ctx }) => {
    const user = await db.select().from(users).where(eq(users.clerkId, ctx.user.clerkId)).limit(1);

    if (!user[0]) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    const resumeCount = await db.select().from(resumes).where(eq(resumes.userId, ctx.user.clerkId));

    return {
      // Basic user info
      id: user[0].id,
      clerkId: user[0].clerkId,
      emailAddress: user[0].emailAddress,
      isPremium: user[0].planId !== "free",
      planId: user[0].planId,

      // Profile info
      firstName: user[0].firstName,
      lastName: user[0].lastName,
      jobTitle: user[0].jobTitle,
      phone: user[0].phone,
      website: user[0].website,
      bio: user[0].bio,
      address: user[0].address,
      street: user[0].street,
      city: user[0].city,
      country: user[0].country,

      // Plan info
      isFree: user[0].planId === "free",
      resumeCount: resumeCount.length,

      // Additional fields
      hasUsedFreeAI: user[0].hasUsedFreeAI,
      pdfExportCount: user[0].pdfExportCount,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    };
  }),

  // Get user plan information
  getPlan: protectedProcedure.query(async ({ ctx }) => {
    const [user] = await db
      .select({
        planId: users.planId,
      })
      .from(users)
      .where(eq(users.clerkId, ctx.user.clerkId))
      .limit(1);

    const resumeCount = await db.select().from(resumes).where(eq(resumes.userId, ctx.user.clerkId));

    return {
      isFree: user.planId === "free",
      resumeCount: resumeCount.length,
    };
  }),

  // Update user profile
  updateProfile: protectedProcedure
    .input(
      z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        jobTitle: z.string().optional(),
        phone: z.string().optional(),
        website: z.string().optional(),
        bio: z.string().optional(),
        address: z.string().optional(),
        street: z.string().optional(),
        city: z.string().optional(),
        country: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const result = await db
        .update(users)
        .set({
          ...input,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, ctx.user.clerkId))
        .returning();

      if (result.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return {
        success: true,
        profile: {
          id: result[0].id,
          firstName: result[0].firstName,
          lastName: result[0].lastName,
          jobTitle: result[0].jobTitle,
          phone: result[0].phone,
          email: result[0].emailAddress,
          website: result[0].website,
          bio: result[0].bio,
          address: result[0].address,
          street: result[0].street,
          city: result[0].city,
          country: result[0].country,
        },
      };
    }),

  // Delete user account
  deleteAccount: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const { clerkId } = ctx.user;

      // First, delete all user's resumes and their nested data
      // Get all user's resumes
      const userResumes = await db.query.resumes.findMany({
        where: eq(resumes.userId, clerkId),
      });

      // Delete all nested resume data
      for (const resume of userResumes) {
        // Delete all resume-related data in sequence
        await db.delete(educations).where(eq(educations.resumeId, resume.id));
        await db.delete(experiences).where(eq(experiences.resumeId, resume.id));
        await db.delete(projects).where(eq(projects.resumeId, resume.id));
        await db.delete(awards).where(eq(awards.resumeId, resume.id));
        await db.delete(certifications).where(eq(certifications.resumeId, resume.id));
        await db.delete(skills).where(eq(skills.resumeId, resume.id));
        await db.delete(languages).where(eq(languages.resumeId, resume.id));
        await db.delete(references).where(eq(references.resumeId, resume.id));
        await db.delete(hobbies).where(eq(hobbies.resumeId, resume.id));
        await db.delete(volunteerings).where(eq(volunteerings.resumeId, resume.id));
        await db.delete(profiles).where(eq(profiles.resumeId, resume.id));
      }

      // Delete all user's websites
      await db.delete(websites).where(eq(websites.userId, clerkId));

      // Delete all user's resumes
      await db.delete(resumes).where(eq(resumes.userId, clerkId));

      // Delete user from our database
      await db.delete(users).where(eq(users.clerkId, clerkId));

      // Finally, delete user from Clerk
      const client = await clerkClient();
      await client.users.deleteUser(clerkId);

      return { success: true };
    } catch (error) {
      console.error("Failed to delete user account:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete account. Please try again or contact support.",
      });
    }
  }),

  // Get user invoices from Paddle
  getInvoices: protectedProcedure.query(async ({ ctx }) => {
    try {
      const { paddleCustomerId } = ctx.user;

      // If user doesn't have a paddleCustomerId, return empty array
      if (!paddleCustomerId) {
        return [];
      }

      // Fetch invoices from Paddle using the user's paddleCustomerId
      const invoices = await paddleServer.getUserTransactions(paddleCustomerId);

      return invoices;
    } catch (error) {
      console.error("Failed to fetch user invoices:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch invoices. Please try again later.",
      });
    }
  }),

  // Revoke premium access
  revokePremiumAccess: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const { clerkId, emailAddress } = ctx.user;

      // Update user to revoke premium access - set back to free plan
      await db
        .update(users)
        .set({
          planId: "free",
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, clerkId));

      console.log(`✅ Premium access revoked for user: ${emailAddress} (${clerkId})`);

      return {
        success: true,
        message: "Premium access has been revoked successfully.",
      };
    } catch (error) {
      console.error("Failed to revoke premium access:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to revoke premium access. Please try again later.",
      });
    }
  }),
});

export type UserRouter = typeof userRouter;
