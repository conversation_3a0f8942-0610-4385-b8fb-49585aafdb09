import { resumesRouter } from "./routers/resumes";
import { sharingRouter } from "./routers/sharing";
import { templatesRouter } from "./routers/templates";
import { userRouter } from "./routers/user";
import { websitesRouter } from "./routers/websites";
import { router } from "./trpc";

export const appRouter = router({
  resumes: resumesRouter,
  sharing: sharingRouter,
  templates: templatesRouter,
  user: userRouter,
  websites: websitesRouter,
});

export type AppRouter = typeof appRouter;
