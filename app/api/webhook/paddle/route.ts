import crypto from "node:crypto";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { users } from "@/db/schema";
import { PaddleEventType, getPlanTypeFromPriceId, type PaddleWebhookPayload } from "@/lib/paddle";
import { updateUserPlanStatus } from "@/lib/user-sync";

// Verify Paddle webhook signature
function verifyWebhookSignature(rawBody: string, signatureHeader: string, secret: string): boolean {
  // Paddle sends the signature in the format: "ts=timestamp;h1=signature"
  const parts = signatureHeader.split(";");
  let timestamp = "";
  const signatures: string[] = [];

  for (const part of parts) {
    const [key, value] = part.split("=");
    if (key === "ts") {
      timestamp = value;
    } else if (key === "h1") {
      signatures.push(value);
    }
  }

  if (!timestamp || signatures.length === 0) {
    console.error("Invalid signature format");
    return false;
  }

  // Construct the signed payload
  const signedPayload = `${timestamp}:${rawBody}`;

  // Calculate the expected signature
  const hmac = crypto.createHmac("sha256", secret);
  hmac.update(signedPayload);
  const expectedSignature = hmac.digest("hex");

  // Check if any of the signatures match
  const isValid = signatures.some((sig) => sig === expectedSignature);

  return isValid;
}

// Helper function to validate webhook signature and secret
async function validateWebhookRequest(
  request: NextRequest,
  rawBody: string,
): Promise<{ success: true } | { success: false; response: NextResponse }> {
  const signature = request.headers.get("paddle-signature");

  if (!signature) {
    console.error("❌ No signature provided");
    return { success: false, response: NextResponse.json({ error: "No signature provided" }, { status: 401 }) };
  }

  const webhookSecret = process.env.PADDLE_WEBHOOK_SECRET;
  if (!webhookSecret) {
    console.error("❌ Paddle webhook secret not configured");
    return { success: false, response: NextResponse.json({ error: "Webhook secret not configured" }, { status: 500 }) };
  }

  const SKIP_SIGNATURE_VERIFICATION = false; // IMPORTANT: Keep this false in production!

  if (!SKIP_SIGNATURE_VERIFICATION && !verifyWebhookSignature(rawBody, signature, webhookSecret)) {
    console.error("❌ Invalid webhook signature");
    return { success: false, response: NextResponse.json({ error: "Invalid signature" }, { status: 401 }) };
  }

  return { success: true };
}

// Helper function to validate user ID from payload
function validateUserId(
  payload: PaddleWebhookPayload,
): { success: true; userId: string } | { success: false; response: NextResponse } {
  const userId = payload.data.custom_data?.userId;

  if (!userId) {
    console.error("❌ No user ID provided in custom data");
    return { success: false, response: NextResponse.json({ error: "No user ID provided" }, { status: 400 }) };
  }

  return { success: true, userId };
}

// Helper function to update user premium status and paddle customer ID
async function updateUserToPremium(
  userId: string,
  planId: string | undefined,
  paymentData: { paymentId: string; paymentGateway: string; subscriptionId?: string },
  paddleCustomerId: string,
  currentPeriodEnd?: string,
) {
  const validPlanId: "pro_monthly" | "pro_yearly" =
    planId && ["pro_monthly", "pro_yearly"].includes(planId) ? (planId as "pro_monthly" | "pro_yearly") : "pro_monthly";

  console.log("👤 Updating user to premium:", { userId, planId: validPlanId });

  const updatedUser = await updateUserPlanStatus(userId, validPlanId, paymentData);

  await db
    .update(users)
    .set({
      paddleCustomerId,
      subscriptionId: paymentData.subscriptionId,
      currentPeriodEnd,
      updatedAt: new Date().toISOString(),
    })
    .where(eq(users.clerkId, userId));

  return { updatedUser, validPlanId };
}

// Helper function to handle transaction completed event
async function handleTransactionCompleted(payload: PaddleWebhookPayload): Promise<NextResponse | null> {
  console.log("💳 Processing transaction completed event");

  const userValidation = validateUserId(payload);
  if (!userValidation.success) {
    return userValidation.response;
  }

  const planId = (payload.data.custom_data as any)?.planId;
  const priceId = payload.data.items?.[0]?.price_id;
  const determinedPlanId = planId || (priceId ? getPlanTypeFromPriceId(priceId) : null);
  
  const { updatedUser, validPlanId } = await updateUserToPremium(
    userValidation.userId,
    determinedPlanId,
    { 
      paymentId: payload.data.id, 
      paymentGateway: "paddle",
      subscriptionId: payload.data.subscription_id 
    },
    payload.data.customer_id || "",
    payload.data.current_billing_period?.ends_at,
  );

  console.log("✅ User premium update completed:", {
    userId: userValidation.userId,
    transactionId: payload.data.id,
    planId: validPlanId,
    updatedUser: updatedUser?.id,
  });

  return null;
}

// Helper function to handle transaction updated event
async function handleTransactionUpdated(payload: PaddleWebhookPayload): Promise<NextResponse | null> {
  console.log("🔄 Processing transaction updated event");

  const userValidation = validateUserId(payload);
  if (!userValidation.success) {
    return userValidation.response;
  }

  const userId = userValidation.userId;
  const planId = (payload.data.custom_data as any)?.planId;

  if (payload.data.status === "completed") {
    const { updatedUser, validPlanId } = await updateUserToPremium(
      userId,
      planId,
      { paymentId: payload.data.id, paymentGateway: "paddle", subscriptionId: payload.data.subscription_id },
      payload.data.customer_id || "",
      payload.data.current_billing_period?.ends_at,
    );

    console.log("✅ User premium update completed via transaction.updated:", {
      userId,
      transactionId: payload.data.id,
      planId: validPlanId,
      updatedUser: updatedUser?.id,
    });
  } else if (payload.data.status === "refunded") {
    console.log("💰 Processing refund for user:", { userId });
    await updateUserPlanStatus(userId, "free");
    console.log("✅ Premium access revoked due to refund:", { userId });
  }

  return null;
}

// Helper function to handle subscription created event
async function handleSubscriptionCreated(payload: PaddleWebhookPayload): Promise<NextResponse | null> {
  console.log("📅 Processing subscription created event");

  const userValidation = validateUserId(payload);
  if (!userValidation.success) {
    return userValidation.response;
  }

  const userId = userValidation.userId;
  const priceId = payload.data.items?.[0]?.price_id;
  const planType = priceId ? getPlanTypeFromPriceId(priceId) : null;

  if (!planType) {
    console.error("❌ Could not determine plan type from price ID:", priceId);
    return NextResponse.json({ error: "Invalid price ID" }, { status: 400 });
  }

  const { updatedUser, validPlanId } = await updateUserToPremium(
    userId,
    planType,
    { 
      paymentId: payload.data.id, 
      paymentGateway: "paddle", 
      subscriptionId: payload.data.subscription_id 
    },
    payload.data.customer_id || "",
    payload.data.current_billing_period?.ends_at,
  );

  console.log("✅ Subscription created:", {
    userId,
    subscriptionId: payload.data.subscription_id,
    planId: validPlanId,
    updatedUser: updatedUser?.id,
  });

  return null;
}

// Helper function to handle subscription updated event
async function handleSubscriptionUpdated(payload: PaddleWebhookPayload): Promise<NextResponse | null> {
  console.log("🔄 Processing subscription updated event");

  const userValidation = validateUserId(payload);
  if (!userValidation.success) {
    return userValidation.response;
  }

  const userId = userValidation.userId;
  const priceId = payload.data.items?.[0]?.price_id;
  const planType = priceId ? getPlanTypeFromPriceId(priceId) : null;

  if (!planType) {
    console.error("❌ Could not determine plan type from price ID:", priceId);
    return NextResponse.json({ error: "Invalid price ID" }, { status: 400 });
  }

  // Update the user's plan (might be an upgrade/downgrade)
  const { updatedUser, validPlanId } = await updateUserToPremium(
    userId,
    planType,
    { 
      paymentId: payload.data.id, 
      paymentGateway: "paddle", 
      subscriptionId: payload.data.subscription_id 
    },
    payload.data.customer_id || "",
    payload.data.current_billing_period?.ends_at,
  );

  console.log("✅ Subscription updated:", {
    userId,
    subscriptionId: payload.data.subscription_id,
    planId: validPlanId,
    updatedUser: updatedUser?.id,
  });

  return null;
}

// Helper function to handle subscription canceled event
async function handleSubscriptionCanceled(payload: PaddleWebhookPayload): Promise<NextResponse | null> {
  console.log("❌ Processing subscription canceled event");

  const userValidation = validateUserId(payload);
  if (!userValidation.success) {
    return userValidation.response;
  }

  const userId = userValidation.userId;

  // Update user to free plan
  await updateUserPlanStatus(userId, "free");

  // Clear subscription data
  await db
    .update(users)
    .set({
      subscriptionId: null,
      currentPeriodEnd: null,
      updatedAt: new Date().toISOString(),
    })
    .where(eq(users.clerkId, userId));

  console.log("✅ Subscription canceled:", {
    userId,
    subscriptionId: payload.data.subscription_id,
  });

  return null;
}

export async function POST(request: NextRequest) {
  try {
    const rawBody = await request.text();

    console.log("🔔 Paddle webhook received:", {
      hasSignature: !!request.headers.get("paddle-signature"),
      bodyLength: rawBody.length,
      timestamp: new Date().toISOString(),
    });

    const validationResult = await validateWebhookRequest(request, rawBody);
    if (!validationResult.success) {
      return validationResult.response;
    }

    const payload: PaddleWebhookPayload = JSON.parse(rawBody);

    console.log("📦 Webhook payload:", {
      eventType: payload.event_type,
      transactionId: payload.data?.id,
      customData: payload.data?.custom_data,
      status: payload.data?.status,
    });

    let errorResponse: NextResponse | null = null;

    switch (payload.event_type) {
      case PaddleEventType.TransactionCompleted:
        errorResponse = await handleTransactionCompleted(payload);
        break;
      case PaddleEventType.TransactionUpdated:
        errorResponse = await handleTransactionUpdated(payload);
        break;
      case PaddleEventType.SubscriptionCreated:
        errorResponse = await handleSubscriptionCreated(payload);
        break;
      case PaddleEventType.SubscriptionUpdated:
        errorResponse = await handleSubscriptionUpdated(payload);
        break;
      case PaddleEventType.SubscriptionCanceled:
        errorResponse = await handleSubscriptionCanceled(payload);
        break;
      default:
        console.log(`📦 Unhandled event type: ${payload.event_type}`);
      // Silently ignore other event types
    }

    if (errorResponse) {
      return errorResponse;
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Paddle webhook error:", error);
    return NextResponse.json({
      received: true,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
