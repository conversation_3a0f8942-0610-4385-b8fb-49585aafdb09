"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>rops } from "@heroui/react";
import { ReactNode } from "react";

interface EnhancedModalProps extends Omit<ModalProps, "children"> {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  headerIcon?: ReactNode;
  footer?: ReactNode | ((onClose: () => void) => ReactNode);
}

export function AppModal({ children, title, subtitle, headerIcon, footer, ...props }: EnhancedModalProps) {
  return (
    <Modal
      {...props}
      classNames={{
        ...props.classNames,
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            {(title || subtitle || headerIcon) && (
              <ModalHeader>
                <div className="flex flex-col gap-1">
                  {(title || headerIcon) && (
                    <div className="flex items-center gap-2">
                      {headerIcon}
                      {title && <h2 className={`text-2xl font-bold text-foreground`}>{title}</h2>}
                    </div>
                  )}
                  {subtitle && <p className="text-sm text-default-500 dark:text-default-400">{subtitle}</p>}
                </div>
              </ModalHeader>
            )}
            <ModalBody>{children}</ModalBody>
              <ModalFooter className={` gap-3`}>{typeof footer === "function" ? footer(onClose) : footer}</ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
