"use client";

import { Icon } from "@iconify/react";
import React, { useState } from "react";
import { trpc } from "@/app/_trpc/client";
import { ColorSchemeSelector, TemplateSelector } from "@/components/shared";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import { ColorSchemeId } from "@/config/color-schemes";
import { useUpgradeModal } from "@/contexts/upgrade-modal-context";
import { FullResume, Template } from "@/db/schema";
import ResumeTemplateRenderer from "../resume/templates/template-registry";

interface TemplateGalleryProps {
  sampleResume: FullResume;
  onTemplateSelect?: (template: Template) => void;
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({ sampleResume, onTemplateSelect }) => {
  const getTemplates = trpc.templates.getTemplates.useQuery({});
  const { isLoading, data: templates = [] } = getTemplates;
  const { data: plan } = trpc.user.getPlan.useQuery();

  const [selectedTemplateId, setSelectedTemplateId] = useState<number>(sampleResume?.templateId || 1);
  const [selectedColorScheme, setSelectedColorScheme] = useState<ColorSchemeId>("blue");
  const { showUpgradeModal } = useUpgradeModal();

  const freeTemplates = templates.filter((t) => !t.isPremium);
  const premiumTemplates = templates.filter((t) => t.isPremium);

  const handleTemplateSelect = (templateId: number) => {
    const template = templates.find((t) => t.id === templateId);
    if (template) {
      if (plan?.isFree && template.isPremium) {
        showUpgradeModal();
        return;
      }
      setSelectedTemplateId(template.id);
      onTemplateSelect?.(template);
    }
  };

  // Create modified resume with selected template and color scheme
  const previewResume: FullResume = {
    ...sampleResume,
    colorScheme: selectedColorScheme,
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-6 gap-8 w-full">
      {/* Template Gallery Sidebar */}
      <div className="lg:col-span-2 w-full lg:max-w-[450px] space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
              <Icon icon="lucide:palette" className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Customize Template</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Choose your template style and color scheme</p>
            </div>
          </div>

          {/* Color Scheme Selector */}
          <div className="mb-8">
            <ColorSchemeSelector
              selectedColorScheme={selectedColorScheme}
              onColorSchemeChange={setSelectedColorScheme}
            />
          </div>

          {/* Template List */}
          <div className="max-h-[60vh] overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoaderComponent />
              </div>
            ) : (
              <>
                <h3 className="text-lg font-semibold mb-4">Free Templates</h3>
                <TemplateSelector
                  className="w-full"
                  selectedTemplateId={selectedTemplateId || 0}
                  templates={freeTemplates}
                  onTemplateSelect={handleTemplateSelect}
                  isFreePlan={plan?.isFree}
                />
                <h3 className="text-lg font-semibold my-4">Premium Templates</h3>
                <TemplateSelector
                  className="w-full"
                  selectedTemplateId={selectedTemplateId || 0}
                  templates={premiumTemplates}
                  onTemplateSelect={handleTemplateSelect}
                  isFreePlan={plan?.isFree}
                />
              </>
            )}
          </div>
        </div>

        {/* Template Features */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Template Features</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Icon icon="lucide:check" className="w-3 h-3 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">ATS-friendly design</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Icon icon="lucide:check" className="w-3 h-3 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Professional design</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Icon icon="lucide:check" className="w-3 h-3 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Easy customization</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Icon icon="lucide:check" className="w-3 h-3 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Multiple color schemes</span>
            </div>
          </div>
        </div>
      </div>

      {/* Template Preview */}
      <div className="lg:col-span-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Preview Header */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <Icon icon="lucide:eye" className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Live Preview</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    See how your resume looks with different templates
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium">
                  Interactive
                </div>
              </div>
            </div>
          </div>

          {/* Preview Content */}
          <div className="p-6 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 min-h-[70vh]">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <LoaderComponent />
              </div>
            ) : (
              <div className="bg-white shadow-2xl mx-auto rounded-lg overflow-hidden transform hover:scale-[1.02] transition-transform duration-300">
                <ResumeTemplateRenderer className="w-full h-full" resume={previewResume} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
