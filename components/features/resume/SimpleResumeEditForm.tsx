"use client";

import { Form } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { Autosave } from "react-autosave";
import { trpc } from "@/app/_trpc/client";
import { FormSection } from "@/components/forms/FormSection";
import { SimpleFormField } from "@/components/forms/SimpleFormField";
import { LoaderComponent } from "@/components/shared/common";
import { createEmptyItem, FORM_SECTIONS } from "@/config/form-sections";
import { useFormStore } from "@/lib/form-store";
import { PersonalFormFields } from "./";
import { FullResume } from "@/db/schema";

export const SimpleResumeEditForm = ({ resumeId }: { resumeId: number }) => {
  const { formData, setFormData, addItemToCollection } = useFormStore();
  const getFullResume = trpc.resumes.getFullResume.useQuery({ id: resumeId });
  const updateResumeMutation = trpc.resumes.updateResume.useMutation();
  const [ currentData, setCurrentData ] = useState<FullResume | null>(null);

  const { isLoading, data: initialData } = getFullResume;

  // Use formData if available, otherwise use initialData
  useEffect(() => {
    if (formData) {
      setCurrentData(formData);
    }
  }, [formData, setFormData]);

  console.log("Current data:", currentData);

  // Populate form store when initial data is loaded
  useEffect(() => {
    if (initialData && !formData) {
      setFormData(initialData);
    }
  }, [initialData, formData, setFormData]);

  const handleAddItem = (sectionId: string) => {
    const section = FORM_SECTIONS.find((s) => s.id === sectionId);
    if (!section || !currentData) return;

    const newItem = createEmptyItem(section, resumeId);
    addItemToCollection(section.collectionName, newItem);
  };

  const handleSubmit = async (_data: FormData) => {
    if (!formData) return;

    try {
      await updateResumeMutation.mutateAsync({
        ...formData,
        id: resumeId,
      });
    } catch (error) {
      console.error("Failed to save form data:", error);
    }
  };

  const renderFormFields = (section: any, item: any, index: number) => (
    <div className="grid grid-cols-2 gap-4 w-full">
      {section.fields.map((field: any) => (
        <div key={field.name} className={`w-full ${field.className}`}>
          <SimpleFormField
            name={field.name}
            type={field.type}
            label={field.labelKey || undefined}
            placeholder={field.placeholder || undefined}
            description={field.description || undefined}
            className={field.className}
            value={item[field.name]}
            collectionName={section.collectionName}
            itemIndex={index}
            resumeData={currentData}
            itemContext={item}
            schemaEntity={section.entity}
          />
        </div>
      ))}
    </div>
  );

  if (isLoading) return <LoaderComponent />;

  return (
    <div className="flex flex-col gap-6 w-full p-6">
      <Autosave interval={1000} data={formData} onSave={() => handleSubmit(new FormData())} />

      <Form action={handleSubmit} className="w-full space-y-6">
        {/* Personal Information Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-3 rtl:gap-x-reverse mb-4">
            <div className="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
              <Icon icon="lucide:user" className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Personal Information</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Fill in your personal details and contact information
              </p>
            </div>
          </div>
          <PersonalFormFields data={currentData} />
        </div>

        {/* Resume Sections */}
        <div className="space-y-4 w-full">
          <div className="flex items-center gap-3 rtl:gap-x-reverse mb-6">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Icon icon="lucide:file-text" className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Resume Sections</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Add and manage your resume sections like experience, education, and skills
              </p>
            </div>
          </div>

          {FORM_SECTIONS.map((section) => {
            const items = (currentData?.[section.collectionName as keyof typeof currentData] as any[]) || [];

            return (
              <FormSection
                key={section.id}
                title={section.titleKey}
                description={section.description || undefined}
                icon={section.icon}
                items={items}
                collectionName={section.collectionName}
                resumeId={resumeId}
                onAddItem={() => handleAddItem(section.id)}
              >
                {(item, index) => renderFormFields(section, item, index)}
              </FormSection>
            );
          })}
        </div>
      </Form>
    </div>
  );
};
