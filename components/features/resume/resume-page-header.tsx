"use client";

import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import PageHeader from "@/components/shared/page-header";
import { RESUME_FILTER_OPTIONS } from "@/config/resume-filters";
import { SortOption } from "@/hooks/use-filter-sort";
import CreateResumeButton from "./createResumeButton";

interface ResumePageHeaderProps {
  resumeCount: number;
  onFilterChange?: (filter: string) => void;
  onSortChange?: (sort: SortOption) => void;
  onExportAll?: () => void;
  onReset?: () => void;
  currentFilter?: string;
  currentSort?: SortOption;
  filteredCount?: number;
}

export default function ResumePageHeader({
  resumeCount,
  onFilterChange,
  onSortChange,
  onExportAll,
  onReset,
  currentFilter,
  currentSort,
  filteredCount,
}: ResumePageHeaderProps) {
  const primaryAction = (
    <>
      <Button
        as={Link}
        href="/templates"
        variant="flat"
        className="bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
        startContent={<Icon icon="tabler:template" className="w-4 h-4" />}
      >
        Browse Templates
      </Button>
      <CreateResumeButton
        size="lg"
        className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-200"
      />
    </>
  );

  return (
    <PageHeader
      icon="tabler:file-text"
      titleKey="My Resumes"
      subtitleKey="Create, manage, and customize your professional resumes"
      itemCount={resumeCount}
      translationNamespace="resumes"
      primaryAction={primaryAction}
      onExportAll={onExportAll}
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      onReset={onReset}
      currentFilter={currentFilter}
      currentSort={currentSort}
      filteredCount={filteredCount}
      filterOptions={RESUME_FILTER_OPTIONS}
      exportDisabled={resumeCount === 0}
      itemType="resumes"
    />
  );
}
