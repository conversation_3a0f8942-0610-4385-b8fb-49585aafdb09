"use client";

import { SignedIn, SignedOut, SignInButton } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON>, Mo<PERSON>, ModalContent } from "@heroui/react";
import { Icon } from "@iconify/react";
import NextLink from "next/link";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { ThemeSwitch } from "@/components/shared";
import { Logo } from "@/components/shared/common/icons";
import { CustomUserButton } from "@/components/shared/custom-user-button";
import { getMainNavigationItems, getUserMenuItems } from "@/config/navbar-menu";
import { getMenuItemColorsFromItem } from "@/lib/utils/colors";
import { useSidebarStore } from "@/stores/sidebar-store";
import { UserDropdown } from "./user-dropdown";

export const MobileSidebarDrawer = () => {
  const pathname = usePathname();
  const { isOpen, close, isMobile } = useSidebarStore();

  // Handle escape key to close drawer
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        close();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isOpen, close]);

  // Check if link is active
  const isActive = (path: string) => {
    const cleanPathname = pathname.replace(/^\/(en|ar)/, "");
    const cleanPath = path.replace(/^\/(en|ar)/, "");
    return cleanPathname === cleanPath || cleanPathname.startsWith(cleanPath + "/");
  };

  const menuItems = getMainNavigationItems();
  const userMenuItems = getUserMenuItems();

  // Only show on mobile
  if (!isMobile) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={close}
      size="full"
      placement="center"
      hideCloseButton
      classNames={{
        base: "m-0 sm:m-0",
        wrapper: "items-start justify-start",
        body: "p-0",
      }}
      aria-label="Mobile navigation menu"
    >
      <ModalContent className="max-w-full h-screen bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Logo className="w-8 h-8" />
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30" />
              </div>
              <p className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                QuickCV
              </p>
            </div>
            <Button
              isIconOnly
              size="sm"
              radius="lg"
              variant="ghost"
              className="text-gray-600 dark:text-gray-400"
              onPress={close}
            >
              <Icon icon="tabler:x" className="w-5 h-5" />
            </Button>
          </div>

          {/* Theme and Settings Section */}
          <div className="flex items-center justify-between px-4 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
                <ThemeSwitch />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Theme</span>
            </div>
            <div className="flex items-center gap-2">
              <SignedOut>
                <SignInButton mode="modal">
                  <Button
                    color="primary"
                    size="sm"
                    variant="shadow"
                    className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                    startContent={<Icon icon="tabler:login" className="w-4 h-4" />}
                  >
                    Sign In
                  </Button>
                </SignInButton>
              </SignedOut>
              <SignedIn>
                <UserDropdown trigger={<CustomUserButton size="sm" />} />
              </SignedIn>
            </div>
          </div>

          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto">
            <div className="flex flex-col gap-2 px-4 py-4">
              {menuItems.map((item) => {
                if (item.signedInOnly) {
                  return (
                    <SignedIn key={item.href}>
                      <Link
                        as={NextLink}
                        href={item.href}
                        className={`
                          w-full px-4 py-3 rounded-xl flex items-center gap-3 transition-all duration-200
                          ${isActive(item.href)
                            ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                          }
                        `}
                        onPress={close}
                      >
                        {(() => {
                          const colors = getMenuItemColorsFromItem(item);
                          return (
                            <div
                              className={`w-12 h-12 rounded-lg flex items-center justify-center ${colors.background}`}
                            >
                              <Icon icon={item.icon} className={`w-6 h-6 ${colors.text}`} />
                            </div>
                          );
                        })()}
                        <div className="flex-1">
                          <p className="font-medium">{item.text}</p>
                        </div>
                      </Link>
                    </SignedIn>
                  );
                }
                return (
                  <Link
                    key={item.href}
                    as={NextLink}
                    href={item.href}
                    className={`
                      w-full px-4 py-3 rounded-xl flex items-center gap-3 transition-all duration-200
                      ${isActive(item.href)
                        ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                      }
                    `}
                    onPress={close}
                  >
                    {(() => {
                      const colors = getMenuItemColorsFromItem(item);
                      return (
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colors.background}`}>
                          <Icon icon={item.icon} className={`w-6 h-6 ${colors.text}`} />
                        </div>
                      );
                    })()}
                    <div className="flex-1">
                      <p className="font-medium">{item.text}</p>
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* User Menu Items */}
            <SignedIn>
              <div className="px-4 py-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-4">
                  Account
                </p>
                <div className="space-y-2">
                  {userMenuItems.map((item) => (
                    <Link
                      key={item.key}
                      href={item.href}
                      className="w-full px-4 py-3 rounded-lg flex items-center gap-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      onPress={close}
                    >
                      <Icon icon={item.icon} className="w-5 h-5" />
                      <span>{item.text}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </SignedIn>
          </div>
        </div>
      </ModalContent>
    </Modal>
  );
};
