"use client";

import { pricingPlans } from "@/config/pricing";
import { PricingCard } from "./pricing-plan";
import { useCheckout } from "@/hooks/use-checkout";

export const PricingTable = () => {
  const { startCheckout, isLoading } = useCheckout();

  const handlePlanSelect = (planId: string) => {
    startCheckout(planId as any);
  };

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
      {pricingPlans.map((plan) => (
        <PricingCard
          key={plan.id}
          plan={plan}
          onSelect={handlePlanSelect}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
};
