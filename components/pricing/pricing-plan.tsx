import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { PricingPlan } from "@/config/pricing";

interface PricingCardProps {
  plan: PricingPlan;
  onSelect?: (planId: string) => void;
  isLoading?: boolean;
  showSelectButton?: boolean;
}

export const PricingCard = ({
  plan,
  onSelect,
  isLoading = false,
  showSelectButton = true
}: PricingCardProps) => {
  const handleSelect = () => {
    if (onSelect && plan.id !== "free") {
      onSelect(plan.id);
    }
  };

  return (
    <div
      className={`rounded-lg border p-6 ${plan.is_most_popular ? "border-purple-500" : "border-gray-200 dark:border-gray-700"}`}
    >
      {plan.is_most_popular && (
        <div className="mb-4">
          <span className="bg-purple-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
            Most Popular
          </span>
        </div>
      )}

      <h3 className="text-lg font-semibold">{plan.name}</h3>
      <p className="mt-2 text-sm text-gray-500">{plan.description}</p>
      <p className="mt-4 text-4xl font-bold">{plan.price}</p>

      <ul className="mt-6 space-y-4">
        {plan.features.map((feature, index) => (
          <li key={`${plan.id}-feature-${index}`} className="flex items-center">
            <Icon icon="heroicons:check-circle-20-solid" className="mr-3 h-5 w-5 text-green-500" />
            <span>{feature}</span>
          </li>
        ))}
      </ul>

      {showSelectButton && (
        <Button
          fullWidth
          className={`mt-6 ${plan.is_most_popular ? "bg-purple-500 hover:bg-purple-600" : "bg-gray-800 hover:bg-gray-900"}`}
          onPress={handleSelect}
          isLoading={isLoading}
          isDisabled={plan.id === "free"}
        >
          {plan.id === "free" ? "Current Plan" : "Choose Plan"}
        </Button>
      )}
    </div>
  );
};
