"use client";

import { pricingPlans, type PlanId } from "@/config/pricing";
import { AppModal } from "@/components/shared/ui";
import { PricingCard } from "../pricing/pricing-plan";
import { useCheckout } from "@/hooks/use-checkout";

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature?: "pdf" | "website" | "general";
}

export function UpgradeModal({ isOpen, onClose, feature = "general" }: UpgradeModalProps) {
  // Get the pro plans from pricing config
  const proPlans = pricingPlans.filter(plan => plan.id !== "free");

  // Use the reusable checkout hook
  const { startCheckout, isLoading } = useCheckout({
    onSuccess: () => {
      onClose();
    },
  });

  const handleCheckout = (planId: string) => {
    startCheckout(planId as PlanId);
  };



  return (
    <AppModal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      title="Upgrade to Pro"
      subtitle="Choose your plan and unlock all pro features"
    >
      {/* Plan Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {proPlans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            onSelect={handleCheckout}
            isLoading={isLoading}
          />
        ))}
      </div>

    </AppModal>
  );
}
