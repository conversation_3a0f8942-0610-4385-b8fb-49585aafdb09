# Paddle Configuration Example
# Copy this to your .env.local and fill in the values from your Paddle dashboard

# Paddle Environment - "sandbox" for testing, "production" for live
NEXT_PUBLIC_PADDLE_ENVIRONMENT=sandbox

# Paddle Client Token - Get this from Paddle Dashboard > Developer Tools > Authentication
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=test_xxxxxxxxxxxxxxxxxxxxxxxxxx

# Product IDs - Create these products in Paddle Dashboard > Catalog > Products
# Pro Monthly Subscription ($9.99/month)
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRICE_ID=pri_01hxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRODUCT_ID=pro_01hxxxxxxxxxxxxxxxxxxxxxxxx

# Pro Yearly Subscription ($99.99/year)
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRICE_ID=pri_01hyyyyyyyyyyyyyyyyyyyyyyyy
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRODUCT_ID=pro_01hyyyyyyyyyyyyyyyyyyyyyyyy

# Server-side API Key - For webhook verification and API calls
# Get this from Paddle Dashboard > Developer Tools > Authentication
PADDLE_API_KEY=pdl_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Webhook Secret - For verifying webhook signatures
# Set this up in Paddle Dashboard > Developer Tools > Notifications
# Webhook URL should be: https://yourdomain.com/api/webhook/paddle
PADDLE_WEBHOOK_SECRET=pdl_whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx