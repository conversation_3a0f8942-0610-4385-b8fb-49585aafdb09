"use client";

import { useCallback, useEffect, useState } from "react";
import type { Paddle } from "@paddle/paddle-js";
import toast from "react-hot-toast";
import { initializePaddle } from "@/lib/paddle";
import type { PlanId } from "@/config/pricing";

interface UseCheckoutOptions {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

interface CheckoutState {
  isLoading: boolean;
  paddle: Paddle | null;
  error: string | null;
}

export function useCheckout(options: UseCheckoutOptions = {}) {
  const [state, setState] = useState<CheckoutState>({
    isLoading: false,
    paddle: null,
    error: null,
  });

  // Initialize Paddle on hook mount
  useEffect(() => {
    const initPaddle = async () => {
      try {
        const paddleInstance = await initializePaddle();
        if (paddleInstance) {
          setState(prev => ({ ...prev, paddle: paddleInstance }));
        }
      } catch (error) {
        console.error("Failed to initialize Paddle:", error);
        setState(prev => ({ 
          ...prev, 
          error: "Failed to initialize payment system" 
        }));
      }
    };
    
    initPaddle();
  }, []);

  const startCheckout = useCallback(async (planId: PlanId) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Validate plan
      if (!planId || planId === "free") {
        throw new Error("Invalid plan selected");
      }

      // Get checkout data from our API
      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          planId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to initialize checkout");
      }

      const { data } = await response.json();

      if (!data.priceId) {
        throw new Error("Price ID is missing from checkout data");
      }

      // Check if Paddle is initialized
      if (!state.paddle) {
        throw new Error("Payment system not initialized");
      }

      // Configure checkout
      const checkoutConfig: any = {
        items: [
          {
            priceId: data.priceId,
            quantity: 1,
          },
        ],
      };

      // Add customer email if available
      if (data.customer?.email) {
        const email = data.customer.email.trim();
        if (email?.includes("@")) {
          checkoutConfig.customer = {
            email: email,
          };
        }
      }

      // Add custom data
      if (data.customData?.userId) {
        checkoutConfig.customData = {
          userId: String(data.customData.userId),
          planId,
        };
      }

      // Add settings with success URL
      checkoutConfig.settings = {
        successUrl: `${window.location.origin}/payment/success?transaction={transaction_id}`,
        allowLogout: false,
        showAddDiscounts: false,
      };

      // Open Paddle checkout
      state.paddle.Checkout.open(checkoutConfig);

      // Call success callback
      options.onSuccess?.();
      
      toast.success("Redirecting to checkout...");

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to start checkout";
      
      console.error("Checkout error:", error);
      setState(prev => ({ ...prev, error: errorMessage }));
      
      // Call error callback
      options.onError?.(errorMessage);
      
      toast.error(errorMessage);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.paddle, options]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    startCheckout,
    isLoading: state.isLoading,
    error: state.error,
    clearError,
    isReady: !!state.paddle,
  };
}
